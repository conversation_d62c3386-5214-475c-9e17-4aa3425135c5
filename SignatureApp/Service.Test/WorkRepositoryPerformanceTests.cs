using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Model.Workflow;
using DataInterface.RepositoryInterfaces;
using Task = System.Threading.Tasks.Task;

namespace Service.Test;

[TestClass]
public class WorkRepositoryPerformanceTests : DatabaseTestsBase
{
    private IMatterRepository _matterRepository = null!;
    private ITaskRepository _taskRepository = null!;
    private IWorkRepository _workRepository = null!;

    [TestInitialize]
    public async Task TestInitialize()
    {
        await InitializeAsync();
        _matterRepository = _serviceProvider.GetRequiredService<IMatterRepository>();
        _taskRepository = _serviceProvider.GetRequiredService<ITaskRepository>();
        _workRepository = _serviceProvider.GetRequiredService<IWorkRepository>();
    }
    [TestMethod]
    public async Task GetNextUnassignedWorkAsync_PerformanceTest()
    {
        int matterId = 35;
        int[] userIds = Enumerable.Range(16, 6).ToArray();

        var matter = await _matterRepository.GetByIdAsync(matterId);
        Assert.IsNotNull(matter);
        var allMatterTasks = await _taskRepository.GetAllByMatterIdAsync(matterId);
        Assert.IsTrue(allMatterTasks.Any());

        var userIdToTasks = new Dictionary<int, List<Model.Workflow.Task>>();
        foreach (var tempUserId in userIds)
        {
            var userTasks = await _taskRepository.GetAllByUserId(tempUserId);
            userIdToTasks[tempUserId] = userTasks;
        }

        var userId = userIds[0];
        var tasks = userIdToTasks[userId];

        // Warm up the query (first execution often slower due to compilation)
        await _workRepository.GetNextUnassignedWorkAsync(tasks, userIds[0]);
        // Enable SQL logging to capture query execution details
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<WorkRepositoryPerformanceTests>();

        // Act - Measure performance
        var stopwatch = Stopwatch.StartNew();
        foreach (var i in Enumerable.Range(0, 10)) // Run multiple times to average out performance
        {
            userId = userIds[i % userIds.Length]; // Cycle through user IDs
            var result = await _workRepository.GetNextUnassignedWorkAsync(tasks, userId);
            Assert.IsNotNull(result);
        }
        stopwatch.Stop();
        var executionTime = stopwatch.ElapsedMilliseconds;

        // Assert performance expectations
        logger.LogInformation($"GetNextUnassignedWorkAsync took {executionTime}ms");

        // Fail if query takes longer than acceptable threshold
        //Assert.IsTrue(executionTime < 1000, $"Query took {executionTime}ms, expected < 1000ms");
    }


    [TestMethod]
    public async Task GetNextUnassignedWorkAsync_IndexAnalysis()
    {
        // This test helps identify which indexes would improve performance
        int matterId = 35;
        var tasks = await _taskRepository.GetAllByMatterIdAsync(matterId);
        Assert.IsTrue(tasks.Any());

        // Test different query patterns to identify bottlenecks
        var stopwatch = new Stopwatch();

        // Test 1: Filter by WorkStatus only
        stopwatch.Restart();
        var worksByStatus = await _context.Works
            .Where(x => x.WorkStatus == WorkStatus.None)
            .CountAsync();
        stopwatch.Stop();
        Console.WriteLine($"WorkStatus filter: {stopwatch.ElapsedMilliseconds}ms, Count: {worksByStatus}");

        // Test 2: Add Matter.IsActive filter
        stopwatch.Restart();
        var worksByMatterActive = await _context.Works
            .Where(x => x.Matter.IsActive == true)
            .Where(x => x.WorkStatus == WorkStatus.None)
            .CountAsync();
        stopwatch.Stop();
        Console.WriteLine($"+ Matter.IsActive filter: {stopwatch.ElapsedMilliseconds}ms, Count: {worksByMatterActive}");

        // Test 3: Add TaskId filter
        stopwatch.Restart();
        var worksByTaskIds = await _context.Works
            .Where(x => x.Matter.IsActive == true)
            .Where(x => x.WorkStatus == WorkStatus.None)
            .Where(x => tasks.Select(t => t.Id).Contains(x.TaskId))
            .CountAsync();
        stopwatch.Stop();
        Console.WriteLine($"+ TaskId filter: {stopwatch.ElapsedMilliseconds}ms, Count: {worksByTaskIds}");
    }
}